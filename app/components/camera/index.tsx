import React, { useImperative<PERSON><PERSON><PERSON>, useRef, useEffect } from "react"
import { StyleSheet } from "react-native"
import {
  Camera,
  CameraRuntimeError,
  CodeType,
  useCameraDevice,
  useCameraFormat,
  useCameraPermission,
  useCodeScanner,
} from "react-native-vision-camera"

interface MyCameraProps {
  photo?: boolean
  video?: boolean
  photoRatio?: number
  flashMode?: boolean
  isActive?: boolean
  isCodeScanned?: boolean
  onCodeScanned?: (codes: any, frame: any) => void
  onCodeScannedTypes?: CodeType[]
}

const MyCamera = (props: MyCameraProps, ref: any) => {
  const {
    photo: isPhoto = true,
    video: isVideo = false,
    photoRatio = 16 / 9,
    flashMode = false,
    isActive = true,
    isCodeScanned = false,
    onCodeScanned = () => {},
    onCodeScannedTypes = ['pdf-417'],
  } = props
  const cameraRef = useRef<Camera>(null)
  const [isCameraReady, setCameraReady] = React.useState(false)
  const { hasPermission, requestPermission } = useCameraPermission()
  const device = useCameraDevice("back")
  const format = useCameraFormat(device, [{ photoAspectRatio: photoRatio }])
  const codeScanner = useCodeScanner({
    codeTypes: onCodeScannedTypes,
    onCodeScanned,
  })

  const onInitialized = () => {
    setCameraReady(true)
  }

  const onError = (error: CameraRuntimeError) => {
    console.error("MyCamera camera error", error)
  }

  const takePicture = async () => {
    const imageData = await cameraRef?.current?.takePhoto?.({
      flash: flashMode ? "on" : "off",
    })
    return imageData
  }

  // Expose functions to parent
  useImperativeHandle(ref, () => ({
    takePicture,
    requestPermission,
    hasPermission,
    isCameraReady,
  }))

  return (
    <Camera
      ref={cameraRef}
      format={format}
      isActive={isActive}
      device={device}
      photo={isPhoto}
      video={isVideo}
      outputOrientation="preview"
      style={StyleSheet.absoluteFill}
      torch={flashMode ? "on" : "off"}
      onError={onError}
      onInitialized={onInitialized}
      codeScanner={!!isCodeScanned ? codeScanner: null}
    />
  )
}

export default React.forwardRef(MyCamera)
